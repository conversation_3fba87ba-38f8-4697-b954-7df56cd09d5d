import boto3
import uuid
import os
from fastapi import UploadFile
import PyPDF2
from docx import Document
import io
from utils.snowflake_setup import get_snowflake_config
import snowflake.connector
from utils.content_processor import ContentProcessor

class FileService:
    def __init__(self):
        # S3 Setup
        self.s3_client = boto3.client(
            's3',
            aws_access_key_id=os.getenv('AWS_ACCESS_KEY_ID'),
            aws_secret_access_key=os.getenv('AWS_SECRET_ACCESS_KEY'),
            region_name=os.getenv('AWS_REGION')
        )
        self.bucket_name = os.getenv('S3_BUCKET_NAME')
        
        # Snowflake Setup
        snowflake_config = get_snowflake_config()
        self.snowflake_conn = snowflake.connector.connect(**snowflake_config)
        
        # Content Processor for enhanced text extraction
        self.content_processor = ContentProcessor()
    
    async def upload_file(self, file: UploadFile):
        doc_id = str(uuid.uuid4())
        
        print(f"Processing file: {file.filename}")
        
        # Upload to S3
        s3_key = f"documents/{doc_id}/{file.filename}"
        file_content = await file.read()
        
        self.s3_client.put_object(
            Bucket=self.bucket_name,
            Key=s3_key,
            Body=file_content,
            ContentType=file.content_type,
            Metadata={
                'doc_id': doc_id,
                'original_filename': file.filename
            }
        )
        print(f"✅ Uploaded to S3: s3://{self.bucket_name}/{s3_key}")
        
        # Extract text
        text_content = await self._extract_text(file_content, file.filename)
        print(f"✅ Extracted {len(text_content)} characters of text")
        
        # Store in Snowflake
        cursor = self.snowflake_conn.cursor()
        cursor.execute("""
            INSERT INTO documents (doc_id, filename, s3_key, file_size, file_type, text_content)
            VALUES (%s, %s, %s, %s, %s, %s)
        """, (doc_id, file.filename, s3_key, len(file_content), file.content_type, text_content))
        
        self.snowflake_conn.commit()
        print(f"✅ Stored metadata in Snowflake")
        
        return {
            "doc_id": doc_id,
            "filename": file.filename,
            "s3_url": f"s3://{self.bucket_name}/{s3_key}",
            "text_content": text_content
        }
    
    async def _extract_text(self, file_content: bytes, filename: str) -> str:
        if filename.lower().endswith('.pdf'):
            return self._extract_pdf_text(file_content)
        elif filename.lower().endswith(('.docx', '.doc')):
            return self._extract_docx_text(file_content)
        elif filename.lower().endswith('.txt'):
            return file_content.decode('utf-8')
        elif filename.lower().endswith(('.png', '.jpg', '.jpeg', '.tiff')):
            return self.content_processor.process_image_content(file_content, filename)
        else:
            raise ValueError(f"Unsupported file type: {filename}")
    
    def _extract_pdf_text(self, file_content: bytes) -> str:
        pdf_file = io.BytesIO(file_content)
        pdf_reader = PyPDF2.PdfReader(pdf_file)
        text = ""
        for page in pdf_reader.pages:
            text += page.extract_text() + "\n"
        
        # Enhanced PDF processing with table extraction
        try:
            # Extract tables from PDF
            tables = self.content_processor.extract_tables_from_pdf(file_content)
            
            # Enhance text with extracted tables
            if tables:
                text = self.content_processor.enhance_text_with_tables(text, tables)
                print(f"✅ Extracted {len(tables)} tables from PDF")
        except Exception as e:
            print(f"⚠️ Table extraction failed: {e}")
            # Continue with basic text extraction
        
        return text
    
    def _extract_docx_text(self, file_content: bytes) -> str:
        doc = Document(io.BytesIO(file_content))
        text = ""
        for paragraph in doc.paragraphs:
            text += paragraph.text + "\n"
        return text 