from langchain_text_splitters import RecursiveCharacterTextSplitter
from sentence_transformers import SentenceTransformer
import uuid
import json
import numpy as np
import openai
import os
from utils.snowflake_setup import get_snowflake_config
import snowflake.connector
from typing import List, Dict, Tuple, Any
from services.reranker import RerankerService
from utils.content_processor import ContentProcessor

class EmbeddingService:
    def __init__(self):
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=1000,
            chunk_overlap=200,
            length_function=len,
        )
        print("Loading embedding model...")
        self.embedding_model = SentenceTransformer('all-MiniLM-L6-v2')
        snowflake_config = get_snowflake_config()
        self.snowflake_conn = snowflake.connector.connect(**snowflake_config)
        
        # Initialize reranker and content processor
        self.reranker = RerankerService()
        self.content_processor = ContentProcessor()
        
        # Configuration for enhanced chunking
        self.SECTION_HEADERS = [
            "ABSTRACT", "INTRODUCTION", "METHODS", "METHOD",
            "RESULTS", "DISCUSSION", "CONCLUSION", "REFERENCES",
            "TABLE", "FIGURE", "SUPPLEMENTARY"
        ]
        
        self.SECTION_WEIGHTS = {
            "METHODS": 1.8, "METHOD": 1.8, "RESULTS": 1.6,
            "TABLE": 1.7, "FIGURE": 1.5,
            "CONCLUSION": 1.4, "DISCUSSION": 1.3,
            "INTRODUCTION": 1.2, "ABSTRACT": 1.1,
            "SUPPLEMENTARY": 1.1, "OTHER": 1.0
        }
        
        print("✅ Embedding service initialized")
    
    async def process_document(self, doc_id: str, text_content: str):
        print(f"Processing document {doc_id} for chunking and embedding...")
        
        # Enhanced chunking with content-aware processing
        # Process mixed content first
        content_blocks = self.content_processor.process_mixed_content(text_content)
        
        all_chunks = []
        for block in content_blocks:
            # Use different chunking strategies based on content type
            if block["type"] == "table":
                # Tables get their own chunks (no further splitting)
                all_chunks.append(block["content"])
            elif block["type"] == "figure":
                # Figures get their own chunks
                all_chunks.append(block["content"])
            else:
                # Regular text gets standard chunking
                text_chunks = self.text_splitter.split_text(block["content"])
                all_chunks.extend(text_chunks)
        
        print(f"Created {len(all_chunks)} chunks from {len(content_blocks)} content blocks")
        
        # Generate embeddings and store
        cursor = self.snowflake_conn.cursor()
        
        for i, chunk in enumerate(all_chunks):
            chunk_id = str(uuid.uuid4())
            
            # Generate embedding
            embedding = self.embedding_model.encode(chunk)
            
            # Store in Snowflake - convert embedding to a format Snowflake can handle
            embedding_list = embedding.tolist()
            # Convert to regular floats to avoid scientific notation and limit precision
            embedding_list = [round(float(x), 6) for x in embedding_list]
            
            # Store as JSON string in TEXT column
            embedding_json = json.dumps(embedding_list)
            
            # Create enhanced metadata for the chunk
            content_type = self.content_processor.classify_content_type(chunk)
            section = self.content_processor.detect_section(chunk)
            metadata = self.content_processor.create_content_metadata(chunk, content_type, section)
            metadata["chunk_index"] = i
            metadata_json = json.dumps(metadata)
            
            try:
                cursor.execute("""
                    INSERT INTO document_chunks 
                    (chunk_id, doc_id, chunk_text, chunk_index, embedding_vector, metadata)
                    VALUES (%s, %s, %s, %s, %s, %s)
                """, (chunk_id, doc_id, chunk, i, embedding_json, metadata_json))
                print(f"Processed chunk {i+1}/{len(all_chunks)}")
            except Exception as e:
                print(f"Error inserting chunk {i}: {e}")
                # Continue with next chunk instead of failing completely
                continue
        
        self.snowflake_conn.commit()
        
        # Update document status
        cursor.execute("UPDATE documents SET status = 'processed' WHERE doc_id = %s", (doc_id,))
        self.snowflake_conn.commit()
        
        print(f"✅ Document {doc_id} processing complete")
        
        return {
            "doc_id": doc_id,
            "chunks_created": len(all_chunks),
            "status": "processed"
        }
    

    
    async def find_relevant_chunks(self, question: str, top_k: int = 5):
        """Enhanced retrieval with reranking and content-type awareness"""
        print(f"Searching for relevant chunks for: {question}")
        
        # Generate question embedding
        question_embedding = self.embedding_model.encode(question)
        question_embedding_list = question_embedding.tolist()
        question_embedding_list = [round(float(x), 6) for x in question_embedding_list]
        
        cursor = self.snowflake_conn.cursor()
        
        try:
            # Get all chunks and compute similarity in Python
            cursor.execute("""
                SELECT 
                    dc.chunk_text,
                    d.filename,
                    dc.embedding_vector,
                    dc.metadata
                FROM document_chunks dc
                JOIN documents d ON dc.doc_id = d.doc_id
                WHERE d.status = 'processed'
            """)
            
            chunks_with_similarity = []
            for chunk_text, filename, embedding_json, metadata_json in cursor.fetchall():
                try:
                    # Parse the stored embedding and metadata
                    stored_embedding = json.loads(embedding_json)
                    metadata = json.loads(metadata_json) if metadata_json else {}
                    
                    # Compute cosine similarity
                    similarity = self._cosine_similarity(question_embedding_list, stored_embedding)
                    
                    # Create chunk object with metadata
                    chunk_obj = {
                        "text": chunk_text,
                        "metadata": metadata
                    }
                    
                    chunks_with_similarity.append((chunk_obj, filename, similarity))
                except Exception as e:
                    print(f"Error processing chunk similarity: {e}")
                    continue
            
            # Apply reranking
            reranked_chunks = await self._rerank_chunks(question, chunks_with_similarity, top_k)
            
        except Exception as e:
            print(f"Error in similarity search: {e}")
            # Fallback to simple text search
            cursor.execute("""
                SELECT 
                    dc.chunk_text,
                    d.filename
                FROM document_chunks dc
                JOIN documents d ON dc.doc_id = d.doc_id
                WHERE d.status = 'processed'
                LIMIT %s
            """, (top_k,))
            
            reranked_chunks = [(chunk[0], chunk[1], 0.5) for chunk in cursor.fetchall()]
        
        if not reranked_chunks:
            print("No relevant documents found")
            return None
        
        print(f"Found {len(reranked_chunks)} relevant chunks")
        
        # Build context and sources
        context = "\n\n".join([f"[From {chunk[1]}]: {chunk[0]}" for chunk in reranked_chunks])
        sources = [f"{chunk[1]} (similarity: {chunk[2]:.2f})" for chunk in reranked_chunks]
        
        return {
            "context": context,
            "sources": sources
        }
    
    async def _rerank_chunks(self, query: str, chunks_with_similarity: List[Tuple], top_k: int) -> List[Tuple]:
        """Apply advanced reranking with section and content-type boosting"""
        if not chunks_with_similarity:
            return []
        
        if len(chunks_with_similarity) <= top_k:
            return chunks_with_similarity
        
        # Convert to format expected by reranker
        chunks_for_reranking = []
        for chunk_obj, filename, base_score in chunks_with_similarity:
            chunks_for_reranking.append({
                "text": chunk_obj["text"],
                "metadata": chunk_obj["metadata"],
                "filename": filename,
                "base_score": base_score
            })
        
        # Apply reranking
        try:
            reranked_results = await self.reranker.batch_rerank(query, chunks_for_reranking, top_k)
            
            # Convert back to original format
            reranked_chunks = []
            for result in reranked_results:
                passage = result["passage"]
                score = result["score"]
                reranked_chunks.append((passage["text"], passage["filename"], score))
            
            return reranked_chunks
            
        except Exception as e:
            print(f"Reranking failed, using base similarity: {e}")
            # Sort by base similarity and return top_k
            chunks_with_similarity.sort(key=lambda x: x[2], reverse=True)
            return chunks_with_similarity[:top_k]
    
    def _cosine_similarity(self, vec1, vec2):
        """Compute cosine similarity between two vectors"""
        try:
            vec1 = np.array(vec1)
            vec2 = np.array(vec2)
            
            # Normalize vectors
            norm1 = np.linalg.norm(vec1)
            norm2 = np.linalg.norm(vec2)
            
            if norm1 == 0 or norm2 == 0:
                return 0.0
            
            # Compute cosine similarity
            similarity = np.dot(vec1, vec2) / (norm1 * norm2)
            return float(similarity)
        except Exception as e:
            print(f"Error computing similarity: {e}")
            return 0.0 