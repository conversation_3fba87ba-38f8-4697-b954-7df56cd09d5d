import os
import logging
from langchain_openai import ChatOpenAI
from langchain_groq import ChatGroq
from langchain.schema.messages import SystemMessage, HumanMessage
from config import Settings

settings = Settings()

class Responsellm:
    def __init__(self):
        # Import here to avoid circular imports
        from services.embedding_service import EmbeddingService
        self.embedding_service = EmbeddingService()
    
    async def generate_response(self, transcript: str, system_prompt: str = None, model: str = None, temperature: float = None, top_p: float = None) -> dict:
        """
        Enhanced response generation with document context and reranking
        """
        try:
            # Use provided parameters or fallback to defaults
            prompt = system_prompt or settings.LLM_SYSTEM_PROMPT
            
            print(f"Generating response for: {transcript}")
            
            # Check for relevant documents with enhanced retrieval
            relevant_docs = await self.embedding_service.find_relevant_chunks(transcript, top_k=10)
            
            # Inject document context if available
            if relevant_docs:
                # Enhanced context with metadata information
                context_with_metadata = self._enhance_context_with_metadata(relevant_docs['context'], relevant_docs['sources'])
                
                # Add content-aware prompts based on query type
                query_type = self._classify_query_type(transcript)
                content_prompt = self._get_content_specific_prompt(query_type, relevant_docs['context'])
                
                prompt += f"\n\nRelevant document context:\n{context_with_metadata}\n\n{content_prompt}"
                print(f"✅ Added enhanced document context with {query_type}-specific prompts")
            else:
                print("✅ No relevant document context found")
            
            # Get LLM with specified parameters
            llm = self.get_llm_provider(model, temperature, top_p)
            
            messages = [
                SystemMessage(content=prompt),
                HumanMessage(content=transcript)
            ]
            
            response = llm.invoke(messages).content
            
            # Return response with enhanced sources if documents were used
            result = {"response": response}
            if relevant_docs:
                result["sources"] = relevant_docs["sources"]
                result["context_used"] = True
                print(f"✅ Response generated with {len(relevant_docs['sources'])} source(s)")
            else:
                result["context_used"] = False
                print("✅ Response generated without document context")
            
            return result
            
        except Exception as e:
            logging.error(f"Response generation error: {e}")
            return {"response": None, "error": str(e)}
    
    def _classify_query_type(self, query: str) -> str:
        """Classify query type for content-specific prompting"""
        query_lower = query.lower()
        
        # Table-related keywords
        table_keywords = ["table", "data", "results", "values", "numbers", "statistics", "chart", "graph"]
        if any(keyword in query_lower for keyword in table_keywords):
            return "table"
        
        # Figure/image-related keywords
        figure_keywords = ["figure", "image", "diagram", "visualization", "picture", "photo"]
        if any(keyword in query_lower for keyword in figure_keywords):
            return "figure"
        
        # Mixed content
        if any(keyword in query_lower for keyword in ["document", "content", "information"]):
            return "mixed"
        
        return "general"
    
    def _get_content_specific_prompt(self, query_type: str, context: str) -> str:
        """Get content-specific prompt based on query type"""
        base_prompt = "Please answer based on this context when relevant."
        
        # Medical expert system message
        medical_expert_prompt = """You are a highly qualified medical expert with extensive knowledge in:
- Clinical medicine and patient care
- Medical research and evidence-based practice
- Diagnostic procedures and laboratory results
- Treatment protocols and medication management
- Medical imaging and radiological findings
- Healthcare regulations and best practices

When analyzing medical documents, reports, or clinical data:
1. Provide accurate, evidence-based medical information
2. Use appropriate medical terminology while remaining accessible
3. Consider clinical context and patient safety
4. Reference specific medical guidelines when applicable
5. Highlight important clinical findings and their implications
6. Maintain professional medical standards in all responses

IMPORTANT: Always include appropriate medical disclaimers and recommend consulting healthcare professionals for specific medical advice."""
        
        if query_type == "table":
            return f"{base_prompt} {medical_expert_prompt} Pay special attention to medical data, laboratory results, clinical statistics, and patient metrics. When referencing medical tables, mention specific values, normal ranges, and clinical significance."
        
        elif query_type == "figure":
            return f"{base_prompt} {medical_expert_prompt} Focus on medical imaging, diagnostic charts, anatomical diagrams, and clinical visualizations. When referencing medical figures, describe findings, anatomical structures, and clinical implications."
        
        elif query_type == "mixed":
            return f"{base_prompt} {medical_expert_prompt} Consider both clinical text and medical data in your response. Reference medical tables, imaging studies, and clinical findings specifically when they contain relevant diagnostic or treatment information."
        
        else:
            return f"{base_prompt} {medical_expert_prompt} If the context contains medical tables, imaging studies, or clinical data, reference them specifically when they support your medical analysis."

    def _enhance_context_with_metadata(self, context: str, sources: list) -> str:
        """Enhance context with metadata information for better LLM understanding"""
        enhanced_context = context
        
        # Add metadata summary if available
        if sources:
            enhanced_context += "\n\n**Source Information:**\n"
            for i, source in enumerate(sources, 1):
                enhanced_context += f"- Source {i}: {source}\n"
        
        return enhanced_context

    @staticmethod
    def get_llm_provider(model: str = None, temperature: float = None, top_p: float = None):
        """Enhanced LLM provider with dynamic parameters"""
        provider = os.getenv("LLM_PROVIDER", "openai").split('#')[0].strip().lower()
        
        # Default values
        default_model = "gpt-4o" if provider == "openai" else "meta-llama/llama-4-scout-17b-16e-instruct"
        default_temp = 0.9
        default_top_p = 0.9
        
        # Use provided parameters or defaults
        model = model or default_model
        temperature = temperature if temperature is not None else default_temp
        top_p = top_p if top_p is not None else default_top_p
        
        if provider == "openai":
            return ChatOpenAI(
                model=model,
                temperature=temperature,
                top_p=top_p,
                api_key=os.getenv("OPENAI_API_KEY")
            )
        elif provider == "groq":
            return ChatGroq(
                model=model,
                temperature=temperature,
                top_p=top_p,
                api_key=os.getenv("GROQ_API_KEY")
            )
        else:
            # Fallback to OpenAI
            return ChatOpenAI(
                model=default_model,
                temperature=default_temp,
                top_p=default_top_p,
                api_key=os.getenv("OPENAI_API_KEY")
            )
    
    # Keep the old method for backward compatibility
    @staticmethod
    async def generate_response_legacy(transcript: str) -> str | None:
        """Legacy method for backward compatibility"""
        try:
            prompt = settings.LLM_SYSTEM_PROMPT
            llm = Responsellm.get_llm_provider()
            messages = [
                SystemMessage(content=prompt),
                HumanMessage(content=transcript)
            ]
            response = llm.invoke(messages).content
            return response
        except Exception as e:
            logging.error(f"Response generation error: {e}")
            return None
