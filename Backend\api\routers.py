from fastapi import APIRouter, HTTPException
from fastapi.responses import JSONResponse, FileResponse
from pydantic import BaseModel
from typing import Optional
from dotenv import load_dotenv
import os
from openai import OpenAI
import traceback

# Load .env once at the top
load_dotenv()

router = APIRouter()


PLUGIN_FILE_MAP = {
    "insight-logger": "logging_utils-0.1.0-py3-none-any.whl",
    "confique": "config_loader-0.1.0-py3-none-any.whl",
    "api-bridge": "frontend_api-0.1.2-py3-none-any.whl",
    "docflow": "docflow-1.0.2-py3-none-any.whl",
    "intelligence-agent": "ai_utility_orchestrator-0.1.0-py3-none-any.whl",
    "vocalis": "voice_assistant-0.1.0-py3-none-any.whl",
}


class ChatRequest(BaseModel):
    model: str
    temperature: float
    top_p: float
    system_prompt: Optional[str] = "You are a helpful assistant."
    user_message: str


@router.post("/api/chat")
async def chat(payload: ChatRequest):
    print("Received payload:", payload)
    try:
        # ✅ Initialize client *inside* the route, after .env is loaded
        client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))

        # Check if the model is GPT-5 variant and adjust parameters accordingly
        is_gpt5_model = payload.model.startswith("gpt-5")

        # Prepare the base parameters
        chat_params = {
            "model": payload.model,
            "messages": [
                {"role": "system", "content": payload.system_prompt},
                {"role": "user", "content": payload.user_message}
            ]
        }

        # Add temperature and top_p only for non-GPT-5 models
        if not is_gpt5_model:
            chat_params["temperature"] = payload.temperature
            chat_params["top_p"] = payload.top_p
        else:
            # GPT-5 models use default values (temperature=1, top_p=1)
            print(f"Using default parameters for GPT-5 model: {payload.model}")

        response = client.chat.completions.create(**chat_params)
        return {"response": response.choices[0].message.content}
    except Exception as e:
        traceback.print_exc()
        return JSONResponse(status_code=500, content={"error": str(e)})


@router.get("/api/plugins/download/{plugin_id}")
async def download_plugin(plugin_id: str):
    base_dir = os.path.dirname(__file__)
    plugins_dir = os.path.abspath(os.path.join(base_dir, "../plugins_repo"))
    filename = PLUGIN_FILE_MAP.get(plugin_id)

    print("[DEBUG] plugin_id:", plugin_id)
    print("[DEBUG] resolved filename:", filename)
    print("[DEBUG] plugins_dir:", plugins_dir)

    if not filename:
        raise HTTPException(status_code=404, detail="Plugin not found in map")

    file_path = os.path.join(plugins_dir, filename)
    print("[DEBUG] full file_path:", file_path)

    if not os.path.isfile(file_path):
        print(f"[ERROR] File not found: {file_path}")
        raise HTTPException(status_code=404, detail="Wheel file missing.")

    return FileResponse(
        path=file_path,
        media_type="application/octet-stream",
        filename=filename
    )
